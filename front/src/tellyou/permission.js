import router from '@/router'
import { useUserStore } from '@/store/modules/user'

// 路由白名单 - 不需要登录就能访问的页面
const whiteList = [
  '/',
  '/login',
  '/register',
  '/about',
  '/contact'
]

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const { token, name } = userStore

  if (token) {
    // 已登录状态
    if (to.path === '/login') {
      // 如果已登录，访问登录页则重定向到首页
      next({ path: '/' })
    } else {
      // 检查是否有用户信息
      if (name) {
        // 有用户信息，直接放行
        next()
      } else {
        // 有token但没有用户信息，需要获取用户信息
        try {
          await userStore.getUserInfo()
          next()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 获取用户信息失败，清除token并跳转到登录页
          await userStore.logout()
          next({ path: '/login', query: { redirect: to.fullPath } })
        }
      }
    }
  } else {
    // 未登录状态
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接放行
      next()
    } else {
      // 不在白名单中，跳转到登录页
      next({ path: '/login', query: { redirect: to.fullPath } })
    }
  }
})

// 路由后置守卫
router.afterEach((to) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - 在线学习平台`
  } else {
    document.title = '在线学习平台'
  }
})
