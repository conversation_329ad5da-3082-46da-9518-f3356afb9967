/**
 * 路由权限控制
 * 前置路由守卫和后置路由守卫
 * 参考tellyou/permission.js设计，简化路由守卫逻辑
 */
import router from '@/router'
import { useUserStore } from '@/store'
import { MessagePlugin } from 'tdesign-vue-next'
import { createApp } from 'vue'
import PageLoading from '@/components/PageLoading.vue'

// 全屏加载状态
let loadingInstance = null
let loadingStartTime = null

// 白名单路由 - 不需要登录就可以访问的页面
const whiteList = ['/login', '/register', '/', '/contact']

/**
 * 前置路由守卫
 */
router.beforeEach(async (to, from, next) => {
  // 开始全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
  }

  loadingStartTime = Date.now()

  // 创建自定义加载组件
  const loadingDiv = document.createElement('div')
  loadingDiv.id = 'page-loading-container'
  document.body.appendChild(loadingDiv)

  const loadingApp = createApp(PageLoading, {
    visible: true,
    text: '页面加载中'
  })

  loadingInstance = loadingDiv
  loadingApp.mount(loadingDiv)

  const userStore = useUserStore()
  const { token, username } = userStore

  if (token) {
    // 已登录状态
    if (to.path === '/login') {
      // 如果已登录，访问登录页则重定向到首页或指定页面
      const redirectPath = to.query.redirect || '/'
      next(decodeURIComponent(redirectPath))
      return
    } else {
      // 检查是否有用户信息
      if (username) {
        // 有用户信息，检查菜单权限
        if (userStore.hasMenuPermission(to.path)) {
          next()
        } else {
          // 没有权限访问该路由
          await MessagePlugin.error('您没有权限访问该页面')
          next('/')
        }
      } else {
        // 有token但没有用户信息，需要获取用户信息
        try {
          await userStore.fetchUserInfo()
          // 获取用户信息后检查权限
          if (userStore.hasMenuPermission(to.path)) {
            next()
          } else {
            await MessagePlugin.error('您没有权限访问该页面')
            next('/')
          }
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 获取用户信息失败，清除token并跳转到登录页
          await userStore.logout()
          await MessagePlugin.error('获取用户信息失败，请重新登录')
          next({ path: '/login', query: { redirect: to.fullPath } })
        }
      }
    }
  } else {
    // 未登录状态
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接放行
      next()
    } else {
      // 不在白名单中，跳转到登录页
      await MessagePlugin.warning('请先登录')
      next({ path: '/login', query: { redirect: to.fullPath } })
    }
  }
})

/**
 * 后置路由守卫
 */
router.afterEach((to, from) => {
  // 结束全屏加载动画
  if (loadingInstance) {
    const loadingDuration = Date.now() - loadingStartTime
    const minLoadingTime = 500 // 最小显示时间500ms
    const additionalDelay = 300 // 额外延迟300ms确保页面渲染完成

    // 计算需要延迟的时间
    const remainingTime = Math.max(0, minLoadingTime - loadingDuration)
    const totalDelay = remainingTime + additionalDelay

    setTimeout(() => {
      if (loadingInstance) {
        document.body.removeChild(loadingInstance)
        loadingInstance = null
        loadingStartTime = null
      }
    }, totalDelay)
  }

  // 设置页面标题
  const defaultTitle = 'MailCode - 临时邮箱服务'
  const routeTitles = {
    '/': 'MailCode - 临时邮箱服务',
    '/login': '登录 - MailCode',
    '/register': '注册 - MailCode',
    '/get-mailbox': '获取邮箱 - MailCode',
    '/my-mailbox': '我的邮箱 - MailCode',
    '/contact': '联系我们 - MailCode'
  }

  document.title = routeTitles[to.path] || defaultTitle

  // 记录路由访问日志（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log(`路由跳转: ${from.path} -> ${to.path}`)
  }

  // 页面滚动到顶部
  window.scrollTo(0, 0)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  // 立即关闭全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
    loadingStartTime = null
  }

  console.error('路由错误:', error)
  MessagePlugin.error('页面加载失败，请刷新重试')
})

/**
 * 获取登录后应该重定向的路径
 */
export const getRedirectPath = () => {
  // 从 URL 参数获取重定向路径
  const urlParams = new URLSearchParams(window.location.search)
  const redirectParam = urlParams.get('redirect')
  if (redirectParam) {
    return decodeURIComponent(redirectParam)
  }

  // 默认返回我的邮箱
  return '/my-mailbox'
}

/**
 * 检查路径是否安全（防止重定向到外部网站）
 */
export const isSafePath = (path) => {
  // 检查是否为相对路径
  if (!path || !path.startsWith('/')) {
    return false
  }

  // 检查是否包含危险字符
  const dangerousPatterns = ['//', 'javascript:', 'data:', 'vbscript:']
  return !dangerousPatterns.some(pattern => path.toLowerCase().includes(pattern))
}

export default router
