import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态 - 使用ref (根据后端user表结构)
  const id = ref(null)
  const username = ref('')
  const email = ref('')
  const password = ref('') // 不存储明文密码，仅用于表单
  const mp_open_id = ref(null)
  const create_time = ref(null)
  const update_time = ref(null)
  const token = ref('')

  // 权限相关状态
  const roles = ref([])
  const permissions = ref([])
  const menus = ref([])
  const isMenuLoaded = ref(false)

  // 计算属性 - 使用computed
  const isLoggedIn = computed(() => !!token.value)

  const userInfo = computed(() => ({
    id: id.value,
    username: username.value,
    email: email.value,
    mp_open_id: mp_open_id.value,
    create_time: create_time.value,
    update_time: update_time.value,
    roles: roles.value,
    permissions: permissions.value
  }))

  const authHeader = computed(() =>
    token.value ? `Bearer ${token.value}` : ''
  )

  // 菜单相关计算属性
  const menuPaths = computed(() => {
    const paths = []
    const extractPaths = (menuList) => {
      menuList.forEach(menu => {
        const path = generatePathFromName(menu.name)
        if (path) {
          paths.push(path)
        }
        if (menu.children && menu.children.length > 0) {
          extractPaths(menu.children)
        }
      })
    }
    extractPaths(menus.value)
    return paths
  })

  const navMenus = computed(() => {
    return menus.value.filter(menu => !menu.is_hidden)
  })

  // 根据菜单名称生成路径的映射
  const generatePathFromName = (name) => {
    const pathMap = {
      'Home': '/',
      'GetMailbox': '/get-mailbox',
      'MyMailbox': '/my-mailbox',
      'Contact': '/contact'
    }
    return pathMap[name] || null
  }

  // 方法 - 普通函数
  function setUserInfo(userData) {
    id.value = userData.id
    username.value = userData.username
    email.value = userData.email || ''
    mp_open_id.value = userData.mp_open_id || null
    create_time.value = userData.create_time || null
    update_time.value = userData.update_time || null
    roles.value = userData.roles || []
    permissions.value = userData.permissions || []
    if (userData.token) {
      token.value = userData.token
    }
  }

  function setToken(newToken) {
    token.value = newToken
  }

  function setMenus(menuList) {
    menus.value = menuList || []
    isMenuLoaded.value = true
  }

  function clearMenus() {
    menus.value = []
    isMenuLoaded.value = false
  }

  function logout() {
    id.value = null
    username.value = ''
    email.value = ''
    password.value = ''
    mp_open_id.value = null
    create_time.value = null
    update_time.value = null
    roles.value = []
    permissions.value = []
    token.value = ''
    clearMenus()
  }

  function updateUsername(newUsername) {
    username.value = newUsername
  }

  function updateEmail(newEmail) {
    email.value = newEmail
  }

  function clearUserData() {
    id.value = null
    username.value = ''
    email.value = ''
    password.value = ''
    mp_open_id.value = null
    create_time.value = null
    update_time.value = null
    roles.value = []
    permissions.value = []
    token.value = ''
    clearMenus()
  }

  // 获取用户信息和菜单
  async function fetchUserInfo() {
    try {
      if (!token.value) {
        throw new Error('未登录')
      }

      const response = await getUserInfo()
      const { user, roles: userRoles, permissions: userPermissions, menus: userMenus } = response.data

      // 更新用户信息
      setUserInfo({
        ...user,
        roles: userRoles,
        permissions: userPermissions
      })

      // 更新菜单信息
      setMenus(userMenus)

      return { user, roles: userRoles, permissions: userPermissions, menus: userMenus }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除本地数据
      logout()
      throw error
    }
  }

  // 权限检查方法 (参考tellyou设计)
  function hasRole(role) {
    return roles.value.some(r => r.code === role || r.name === role)
  }

  function hasPermission(permission) {
    return permissions.value.includes(permission)
  }

  function getDisplayName() {
    return username.value || '未登录'
  }

  // 菜单权限检查方法
  function hasMenuPermission(path) {
    // 静态路由（登录、注册）始终允许访问
    const staticRoutes = ['/login', '/register']
    if (staticRoutes.includes(path)) {
      return true
    }
    // 检查是否在用户菜单权限内
    return menuPaths.value.includes(path)
  }

  // 根据路径查找菜单项
  function findMenuByPath(path) {
    const findInMenus = (menuList) => {
      for (const menu of menuList) {
        const menuPath = generatePathFromName(menu.name)
        if (menuPath === path) {
          return menu
        }
        if (menu.children && menu.children.length > 0) {
          const found = findInMenus(menu.children)
          if (found) return found
        }
      }
      return null
    }
    return findInMenus(menus.value)
  }

  // 返回所有需要暴露的状态和方法
  return {
    // 状态
    id,
    username,
    email,
    password,
    mp_open_id,
    create_time,
    update_time,
    token,
    roles,
    permissions,
    menus,
    isMenuLoaded,

    // 计算属性
    isLoggedIn,
    userInfo,
    authHeader,
    menuPaths,
    navMenus,

    // 方法
    setUserInfo,
    setToken,
    setMenus,
    clearMenus,
    logout,
    updateUsername,
    updateEmail,
    clearUserData,
    fetchUserInfo,
    hasRole,
    hasPermission,
    getDisplayName,
    hasMenuPermission,
    findMenuByPath,
    generatePathFromName
  }
}, {
  // 持久化配置
  persist: {
    key: 'mailcode-user',
    storage: localStorage,
    paths: ['id', 'username', 'email', 'mp_open_id', 'create_time', 'update_time', 'token'] // 持久化用户基本信息，不持久化权限和菜单
  }
})
